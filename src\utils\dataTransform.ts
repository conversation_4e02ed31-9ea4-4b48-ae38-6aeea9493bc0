import { PinData } from '@/components/PinCard';
import { JsonPinData, JsonPinsData } from '@/types/jsonData';
import { generateAvatar } from '@/utils/avatarUtils';

// Unix时间戳转ISO格式
export const unixToISO = (unixTimestamp: number): string => {
  return new Date(unixTimestamp * 1000).toISOString();
};

// 检测是否为GIF文件
const isGifUrl = (url: string): boolean => {
  return url.toLowerCase().includes('.gif');
};

// 检测是否为无效的图片URL
const isInvalidImageUrl = (url: string): boolean => {
  // 过滤掉无法正常显示的图片URL
  const invalidDomains = [
    'https://multimedia.nt.qq.com.cn'
  ];

  return invalidDomains.some(domain => url.startsWith(domain));
};

// 提取内容和类型，返回null表示应该过滤掉这个消息
export const extractContent = (content: JsonPinData['content']) => {
  // 收集所有文本内容
  const textItems = content.filter(item => item.type === 'text' && item.data.text);

  // 如果有文本内容，拼接所有文本
  if (textItems.length > 0) {
    const combinedText = textItems
      .map(item => item.data.text)
      .filter(text => text && text.trim()) // 过滤空文本
      .join(''); // 直接拼接，不添加额外空格

    if (combinedText.trim()) {
      return {
        contentType: 'text' as const,
        content: combinedText,
        imageUrl: undefined
      };
    }
  }

  // 如果没有有效文本内容，查找图片内容
  const imageContent = content.find(item => item.type === 'image');
  if (imageContent?.data.url) {
    // 检查是否为无效的图片URL，如果是则跳过此内容
    if (isInvalidImageUrl(imageContent.data.url)) {
      return null; // 返回null表示应该过滤掉这个消息
    }

    // 检测是否为GIF动图
    const isGif = isGifUrl(imageContent.data.url);

    return {
      contentType: isGif ? 'gif' as const : 'image' as const,
      content: '', // 图片/动图消息可能没有文字描述
      imageUrl: imageContent.data.url
    };
  }

  // 默认返回空文本
  return {
    contentType: 'text' as const,
    content: '暂无内容',
    imageUrl: undefined
  };
};

// 单个数据转换函数，返回null表示应该过滤掉这个消息
export const transformJsonPin = (jsonPin: JsonPinData, index: number): PinData | null => {
  const contentResult = extractContent(jsonPin.content);

  // 如果内容提取返回null，说明应该过滤掉这个消息
  if (contentResult === null) {
    return null;
  }

  const { contentType, content, imageUrl } = contentResult;

  return {
    id: `pin_${index}_${jsonPin.operator_time}`,
    content,
    contentType,
    imageUrl,
    timestamp: unixToISO(jsonPin.operator_time),
    setter: {
      name: jsonPin.operator_nick,
      avatar: generateAvatar(jsonPin.operator_nick)
    },
    sender: {
      name: jsonPin.sender_nick,
      avatar: generateAvatar(jsonPin.sender_nick)
    },
    groupName: undefined // JSON数据中没有群组信息，可以后续添加
  };
};

// 批量数据转换函数，自动过滤掉无效的消息
export const transformJsonPins = (jsonPins: JsonPinsData): PinData[] => {
  return jsonPins
    .map((jsonPin, index) => transformJsonPin(jsonPin, index))
    .filter((pin): pin is PinData => pin !== null); // 过滤掉null值
};
